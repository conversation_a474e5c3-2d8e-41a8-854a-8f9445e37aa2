#!/bin/bash

methods=("all_at_once" "step_by_step" "binary_search")
model="gpt-4o"

# Run the experiment 4 times
for run_num in {1..4}; do
    echo "🚀 Starting run $run_num/4..."

    # Create output directories for this run
    mkdir -p "outputs/with_gt/$run_num"
    mkdir -p "outputs/without_gt/$run_num"

    for method in "${methods[@]}"; do
        echo "🔄 Running $method (run $run_num)..."

        # # Run inference WITH ground truth
        # echo "  📊 Running with_gt inference..."

        # # Handcrafted data
        # python inference_cloudgpt_with_gt.py --method $method --model $model --azure_endpoint "https://cloudgpt-openai.azure-api.net/" --api_version "2025-04-01-preview" --is_handcrafted True --directory_path "../Who&When/Hand-Crafted" --output_dir "outputs/with_gt/$run_num"

        # # Algorithm-generated data
        # python inference_cloudgpt_with_gt.py --method $method --model $model --azure_endpoint "https://cloudgpt-openai.azure-api.net/" --api_version "2025-04-01-preview" --is_handcrafted False --directory_path "../Who&When/Algorithm-Generated" --output_dir "outputs/with_gt/$run_num"

        # Run inference WITHOUT ground truth
        echo "  📊 Running without_gt inference..."

        # # Handcrafted data
        # python inference_cloudgpt_without_gt.py --method $method --model $model --azure_endpoint "https://cloudgpt-openai.azure-api.net/" --api_version "2025-04-01-preview" --is_handcrafted True --directory_path "../Who&When/Hand-Crafted" --output_dir "outputs/without_gt/$run_num"

        # Algorithm-generated data
        python inference_cloudgpt_without_gt.py --method $method --model $model --azure_endpoint "https://cloudgpt-openai.azure-api.net/" --api_version "2025-04-01-preview" --is_handcrafted False --directory_path "../Who&When/Algorithm-Generated" --output_dir "outputs/without_gt/$run_num"
    done

    echo "✅ Run $run_num completed!"
done

echo "🎉 All 4 runs completed!"
