Evaluation Log - 2025-08-05 13:58:50
==================================================

Info: Could not parse valid Step Number for 1.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 2.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 3.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 4.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 5.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 6.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 7.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 8.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 9.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 10.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 11.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 12.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 13.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 14.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 15.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 17.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 18.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 19.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 20.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 21.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 22.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 23.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 24.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 25.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 26.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 27.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 28.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 29.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 30.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 31.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 32.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 33.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 34.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 35.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 36.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 37.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 38.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 39.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 40.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 42.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 43.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 44.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 45.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 46.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 48.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 49.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 50.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 51.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 52.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 53.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 54.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 55.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 56.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 57.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 59.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 60.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 62.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 64.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 65.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 66.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 68.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 69.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 70.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 71.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 72.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 73.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 74.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 75.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 76.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 77.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 78.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 79.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 80.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 81.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 82.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 83.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 84.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 85.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 86.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 88.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 89.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 90.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 92.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 93.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 94.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 95.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 96.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 97.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 98.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 99.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 100.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 101.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 104.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 106.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 107.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 108.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 109.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 110.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 111.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 112.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 113.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 114.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 115.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 116.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 117.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 119.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 120.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 121.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 122.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 123.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 124.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 125.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 126.json, but Agent Name parsed successfully
--- Predictions Read from ../outputs_new/AAO/add_step_idx_v2/without_gt/2/all_at_once_gpt-4o_alg_generated.txt ---
Successfully parsed predictions for 126 files.
=======================================

--- Starting Evaluation ---
Total reference JSON files found in ../../Who&When/Algorithm-Generated: 126
Predictions available for 126 files.
=======================================

--- Evaluation Summary ---
Total reference files in data_path: 126
Predictions parsed from eval file:  126
Files evaluated (prediction found & actual data read): 126
Correct Agent Predictions: 72
Correct Step Predictions:  2

--- Final Accuracy Results ---
Evaluation File: ../outputs_new/AAO/add_step_idx_v2/without_gt/2/all_at_once_gpt-4o_alg_generated.txt
Data Path:       ../../Who&When/Algorithm-Generated
Agent Accuracy: 57.14%
Step Accuracy:  1.59%
(Accuracy calculated based on 126 total files in data path)

Output saved to: ../outputs_new/AAO/add_step_idx_v2/without_gt/2/evaluation_all_at-once-gpt-4o-alg_generated.txt
