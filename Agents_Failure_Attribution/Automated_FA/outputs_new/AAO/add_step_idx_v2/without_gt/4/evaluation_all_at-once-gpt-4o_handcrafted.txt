Evaluation Log - 2025-08-05 13:49:27
==================================================

Info: Could not parse valid Step Number for 1.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 2.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 3.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 5.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 6.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 7.json, but Agent Name parsed successfully
Warning: Could not parse Agent Name for 8.json in ../outputs_new/AAO/add_step_idx_v2/without_gt/4/all_at_once_gpt-4o_handcrafted.txt
Warning: Could not parse Agent Name for 9.json in ../outputs_new/AAO/add_step_idx_v2/without_gt/4/all_at_once_gpt-4o_handcrafted.txt
Warning: Could not parse Agent Name for 11.json in ../outputs_new/AAO/add_step_idx_v2/without_gt/4/all_at_once_gpt-4o_handcrafted.txt
Info: Could not parse valid Step Number for 12.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 13.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 14.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 15.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 16.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 17.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 18.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 19.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 20.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 21.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 23.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 24.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 25.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 26.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 27.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 28.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 29.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 30.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 31.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 33.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 34.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 35.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 36.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 37.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 38.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 39.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 40.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 41.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 42.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 43.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 44.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 45.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 48.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 49.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 51.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 53.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 54.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 55.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 56.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 57.json, but Agent Name parsed successfully
Info: Could not parse valid Step Number for 58.json, but Agent Name parsed successfully
--- Predictions Read from ../outputs_new/AAO/add_step_idx_v2/without_gt/4/all_at_once_gpt-4o_handcrafted.txt ---
Successfully parsed predictions for 55 files.
=======================================

--- Starting Evaluation ---
Total reference JSON files found in ../../Who&When/Hand-Crafted: 58
Predictions available for 55 files.
=======================================

--- Evaluation Summary ---
Total reference files in data_path: 58
Predictions parsed from eval file:  55
Files evaluated (prediction found & actual data read): 55
Correct Agent Predictions: 34
Correct Step Predictions:  1

--- Final Accuracy Results ---
Evaluation File: ../outputs_new/AAO/add_step_idx_v2/without_gt/4/all_at_once_gpt-4o_handcrafted.txt
Data Path:       ../../Who&When/Hand-Crafted
Agent Accuracy: 58.62%
Step Accuracy:  1.72%
(Accuracy calculated based on 58 total files in data path)

Output saved to: ../outputs_new/AAO/add_step_idx_v2/without_gt/4/evaluation_all_at-once-gpt-4o_handcrafted.txt
