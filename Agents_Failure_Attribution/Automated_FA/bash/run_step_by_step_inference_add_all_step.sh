#!/bin/bash

model="gpt-4o"
method="step_by_step"
prompt_template="add_all_step"

# Run the experiment 3 times
for run_num in {1..3}; do
    echo "🚀 Starting run $run_num/3 (Step-by-Step with add_all_step)..."

    # Create output directories for this run
    mkdir -p "../outputs_new/SBS/add_all_step/with_gt/$run_num"
    mkdir -p "../outputs_new/SBS/add_all_step/without_gt/$run_num"

    echo "🔄 Running $method with $prompt_template template (run $run_num)..."

    # Run inference WITH ground truth
    echo "  📊 Running with_gt inference..."
    
    # Handcrafted data
    python ../inference_cloudgpt_optimal.py --method $method --model $model --azure_endpoint "https://cloudgpt-openai.azure-api.net/" --api_version "2025-04-01-preview" --is_handcrafted True --directory_path "../../Who&When/Hand-Crafted" --output_dir "../outputs_new/SBS/add_all_step/with_gt/$run_num" --prompt_template $prompt_template --with_gt True
    
    # Algorithm-generated data
    python ../inference_cloudgpt_optimal.py --method $method --model $model --azure_endpoint "https://cloudgpt-openai.azure-api.net/" --api_version "2025-04-01-preview" --is_handcrafted False --directory_path "../../Who&When/Algorithm-Generated" --output_dir "../outputs_new/SBS/add_all_step/with_gt/$run_num" --prompt_template $prompt_template --with_gt True

    # Run inference WITHOUT ground truth
    echo "  📊 Running without_gt inference..."
    
    # Handcrafted data
    python ../inference_cloudgpt_optimal.py --method $method --model $model --azure_endpoint "https://cloudgpt-openai.azure-api.net/" --api_version "2025-04-01-preview" --is_handcrafted True --directory_path "../../Who&When/Hand-Crafted" --output_dir "../outputs_new/SBS/add_all_step/without_gt/$run_num" --prompt_template $prompt_template --with_gt False
    
    # Algorithm-generated data
    python ../inference_cloudgpt_optimal.py --method $method --model $model --azure_endpoint "https://cloudgpt-openai.azure-api.net/" --api_version "2025-04-01-preview" --is_handcrafted False --directory_path "../../Who&When/Algorithm-Generated" --output_dir "../outputs_new/SBS/add_all_step/without_gt/$run_num" --prompt_template $prompt_template --with_gt False

    echo "✅ Run $run_num completed!"
done

echo "🎉 All 3 runs completed for Step-by-Step with add_all_step template!"
echo "📁 Results saved to: outputs_new/SBS/add_all_step/"
